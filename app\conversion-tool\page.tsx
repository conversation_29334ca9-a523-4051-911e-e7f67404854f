"use client"

import { useState, useEffect } from "react"
import SharedLayout from "@/components/shared-layout"
import ConversionBackground from "@/components/conversion-background"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ArrowLeftRight, Info, ChevronsUpDown } from "lucide-react"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command"
import { Check } from "lucide-react"
import { cn } from "@/lib/utils"
import Image from "next/image"
import { useIsMobile } from "@/hooks/use-mobile"

// Exchange rate API interface
interface ExchangeRateResponse {
  result: string
  conversion_rates: Record<string, number>
}

// Conversion rates based on server pricing
const CONVERSION_RATES = {
  RP_TO_EUR: {
    575: 4.99,
    1380: 10.99,
    2800: 21.99,
    4500: 34.99,
    6500: 49.99,
    13500: 99.99,
    33500: 244.99,
    60200: 429.99
  },
  RP_TO_USD: {
    575: 4.99,
    1380: 10.99,
    2800: 21.99,
    4500: 34.99,
    6500: 49.99,
    13500: 99.99,
    33500: 244.99,
    60200: 429.99
  },
  RP_TO_AUD: {
    575: 7.49,
    1380: 16.49,
    2800: 32.99,
    4500: 52.49,
    6500: 74.99,
    13500: 149.99,
    33500: 365,
    60200: 650
  },
  RP_TO_BRL: {
    575: 18.90,
    1380: 41.90,
    2800: 83.90,
    4500: 132.90,
    6500: 189.90,
    13500: 379.90,
    33500: 925,
    60200: 1650
  },
  RP_TO_TRY: {
    575: 120,
    1380: 250,
    2800: 500,
    4500: 800,
    6500: 1150,
    13500: 2300,
    33500: 5625,
    60200: 9950
  },
  RP_TO_JPY: {
    525: 610,
    1450: 1600,
    2825: 3060,
    5200: 5500,
    7250: 7500,
    15000: 15000,
    33500: 33075,
    60200: 58250
  },
  RP_TO_RUB: {
    575: 490,
    1380: 1090,
    2800: 2175,
    4500: 3450,
    6500: 4950,
    13500: 9900,
    33500: 24250,
    60200: 42750
  },
  RP_TO_USD_LAS: {
    575: 4.25,
    1380: 9.25,
    2800: 18.50,
    4500: 29.50,
    6500: 41.99,
    13500: 84.99,
    33500: 210,
    60200: 370
  },
  RP_TO_USD_LAN: {
    575: 4.25,
    1380: 9.25,
    2800: 18.50,
    4500: 29.50,
    6500: 41.99,
    13500: 84.99,
    33500: 210,
    60200: 370
  },
  RP_TO_GBP: {
    575: 4.5,
    1380: 10.25,
    2800: 19.99,
    4500: 31.50,
    6500: 44.99,
    13500: 88.99,
    33500: 220,
    60200: 385
  },
  AS_TO_RP: 400, // 1 Ancient Spark = 400 RP
  ME_TO_RP: 69.5, // 1 Mythic Essence ≈ 69.5 RP (based on shop bundles)
  RP_TO_BE: 14.545, // 1 RP ≈ 14.545 BE (1650 RP = 24,000 BE)
  RP_TO_OE: 0.667  // 1 RP ≈ 0.667 OE (1650 RP = 1,100 OE)
}

// RP Package data for recommendations
interface RpPackage {
  rp: number
  price: number
  image: string
  efficiency?: number
}

const RP_PACKAGES: RpPackage[] = [
  { rp: 575, price: 4.99, image: '/575.png' },
  { rp: 1380, price: 10.99, image: '/1380.png' },
  { rp: 2800, price: 21.99, image: '/2800.png' },
  { rp: 4500, price: 34.99, image: '/4500.png' },
  { rp: 6500, price: 49.99, image: '/6500.png' },
  { rp: 13500, price: 99.99, image: '/13500.png' },
  { rp: 33500, price: 244.99, image: '/33500.png' },
  { rp: 60200, price: 429.99, image: '/60200.png' }
]

// Exchange rates from USD to other currencies (from exchangerate-api.com)
// Updated: 2025-07-20
const USD_EXCHANGE_RATES = {
  USD: 1,
  EUR: 0.8601,
  GBP: 0.7450,
  JPY: 148.6969,
  CAD: 1.3721,
  AUD: 1.5354,
  CHF: 0.8014,
  SEK: 9.6709,
  NOK: 10.1798,
  DKK: 6.4159,
  PLN: 3.6519,
  CZK: 21.1828,
  HUF: 343.0268,
  RON: 4.3550,
  BGN: 1.6821,
  EGP: 49.4161,
  TRY: 40.4040,
  RUB: 78.2912,
  BRL: 5.5531
}

// In-game currencies
interface InGameCurrency {
  id: string
  name: string
  icon: string
  placeholder: string
}

const IN_GAME_CURRENCIES: InGameCurrency[] = [
  {
    id: 'rp',
    name: 'Riot Points',
    icon: 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/rp.svg',
    placeholder: 'Enter RP amount'
  },
  {
    id: 'as',
    name: 'Ancient Sparks',
    icon: 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/currency/icons/ancient_spark.svg',
    placeholder: 'Enter AS amount'
  },
  {
    id: 'me',
    name: 'Mythic Essence',
    icon: 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/mythic.svg',
    placeholder: 'Enter ME amount'
  },
  {
    id: 'be',
    name: 'Blue Essence',
    icon: '/BlueEssence.svg',
    placeholder: 'Enter BE amount'
  },
  {
    id: 'oe',
    name: 'Orange Essence',
    icon: '/OrangeEssence.svg',
    placeholder: 'Enter OE amount'
  }
]

// Real world currencies
interface RealCurrency {
  id: string
  name: string
  symbol: string
  code: string
  supported: boolean
}

// Conversion info interface
interface ConversionInfo {
  rate: string
  bidirectional: boolean
  supported: boolean
  isSameCurrency?: boolean
}

const REAL_CURRENCIES: RealCurrency[] = [
  // Available currencies (with manual RP conversion rates)
  { id: 'eur', name: 'Euro', symbol: '€', code: 'EUR', supported: true },
  { id: 'usd', name: 'US Dollar', symbol: '$', code: 'USD', supported: true },
  { id: 'aud', name: 'Australian Dollar', symbol: 'A$', code: 'AUD', supported: true },
  { id: 'brl', name: 'Brazilian Real', symbol: 'R$', code: 'BRL', supported: true },
  { id: 'try', name: 'Turkish Lira', symbol: '₺', code: 'TRY', supported: true },
  { id: 'jpy', name: 'Japanese Yen', symbol: '¥', code: 'JPY', supported: true },
  { id: 'rub', name: 'Russian Ruble', symbol: '₽', code: 'RUB', supported: true },
  // Available currencies (using exchange rate API)
  { id: 'gbp', name: 'British Pound', symbol: '£', code: 'GBP', supported: true },
  { id: 'cad', name: 'Canadian Dollar', symbol: 'C$', code: 'CAD', supported: true },
  { id: 'chf', name: 'Swiss Franc', symbol: 'CHF', code: 'CHF', supported: true },
  { id: 'sek', name: 'Swedish Krona', symbol: 'SEK', code: 'SEK', supported: true },
  { id: 'nok', name: 'Norwegian Krone', symbol: 'NOK', code: 'NOK', supported: true },
  { id: 'dkk', name: 'Danish Krone', symbol: 'DKK', code: 'DKK', supported: true },
  { id: 'pln', name: 'Polish Złoty', symbol: 'PLN', code: 'PLN', supported: true },
  { id: 'czk', name: 'Czech Koruna', symbol: 'CZK', code: 'CZK', supported: true },
  { id: 'huf', name: 'Hungarian Forint', symbol: 'HUF', code: 'HUF', supported: true },
  { id: 'ron', name: 'Romanian Leu', symbol: 'RON', code: 'RON', supported: true },
  { id: 'bgn', name: 'Bulgarian Lev', symbol: 'BGN', code: 'BGN', supported: true },
  { id: 'egp', name: 'Egyptian Pound', symbol: 'e£', code: 'EGP', supported: true }
]

// Server regions
interface ServerRegion {
  id: string
  name: string
  supported: boolean
}

const SERVER_REGIONS: ServerRegion[] = [
  // Available servers
  { id: 'euw', name: 'Europe West', supported: true },
  { id: 'eune', name: 'Europe Nordic & East', supported: true },
  { id: 'na', name: 'North America', supported: true },
  { id: 'oce', name: 'Oceania', supported: true },
  { id: 'br', name: 'Brazil', supported: true },
  { id: 'tr', name: 'Turkey', supported: true },
  { id: 'me', name: 'Middle East', supported: true },
  { id: 'jp', name: 'Japan', supported: true },
  { id: 'ru', name: 'Russia', supported: true },
  { id: 'las', name: 'Latin America South', supported: true },
  { id: 'lan', name: 'Latin America North', supported: true },
  { id: 'tw', name: 'Taiwan', supported: true },
  { id: 'sea', name: 'Southeast Asia', supported: true },
  // Coming soon
  { id: 'kr', name: 'Korea', supported: false },
  { id: 'vn', name: 'Vietnam', supported: false }
]

type ConversionMode = 'ingame-to-real' | 'ingame-to-ingame'

// Jokes for when user tries to convert same currency to itself
const SAME_CURRENCY_JOKES = [
  "Congratulations! You've successfully converted your currency to... itself! 🎉 That's a 1:1 exchange rate with 0% fees!",
  "Wow! You've discovered the most efficient conversion ever: 100% accuracy, 0% loss! 🏆",
  "Breaking news: Local summoner achieves perfect conversion rate by converting currency to the same currency! 📰",
  "Achievement unlocked: Master of Redundancy! You've converted something into exactly what it already was! 🏅",
  "Fun fact: This is the only conversion that Riot Games guarantees will never change! 💯",
  "You've just performed the mathematical equivalent of asking 'What's 1 + 0?' Spoiler alert: it's still 1! 🤓"
]

export default function ConversionToolPage() {
  const [conversionMode, setConversionMode] = useState<ConversionMode>('ingame-to-real')
  const [selectedInGameCurrency, setSelectedInGameCurrency] = useState('rp')
  const [selectedTargetInGameCurrency, setSelectedTargetInGameCurrency] = useState('as')
  const [selectedRealCurrency, setSelectedRealCurrency] = useState('eur')
  const [selectedServer, setSelectedServer] = useState('euw')
  const [inGameAmount, setInGameAmount] = useState("")
  const [targetAmount, setTargetAmount] = useState("")
  const [openInGame, setOpenInGame] = useState(false)
  const [openTargetInGame, setOpenTargetInGame] = useState(false)
  const [openReal, setOpenReal] = useState(false)
  const [openServer, setOpenServer] = useState(false)
  const [liveExchangeRates, setLiveExchangeRates] = useState<Record<string, number> | null>(null)
  const [exchangeRatesLoading, setExchangeRatesLoading] = useState(false)

  // Mobile detection to prevent keyboard from opening on dropdown clicks
  const isMobile = useIsMobile()

  // Handle pre-selected currencies from navigation menu
  useEffect(() => {
    const checkStoredSelection = () => {
      const storedSelection = localStorage.getItem('conversionToolSelection')
      if (storedSelection) {
        try {
          const { from, to, mode } = JSON.parse(storedSelection)

          // Check if currencies are in-game or real currencies
          const fromCurrency = IN_GAME_CURRENCIES.find(c => c.id === from)
          const toCurrency = IN_GAME_CURRENCIES.find(c => c.id === to)
          const toRealCurrency = REAL_CURRENCIES.find(c => c.id === to)

          if (mode === 'ingame-to-ingame' && fromCurrency && toCurrency) {
            // Set to in-game to in-game mode
            setConversionMode('ingame-to-ingame')
            setSelectedInGameCurrency(from)
            setSelectedTargetInGameCurrency(to)
            // Clear input fields when switching currencies
            setInGameAmount("")
            setTargetAmount("")
          } else if (mode === 'ingame-to-real' && fromCurrency && toRealCurrency) {
            // Set to in-game to real currency mode
            setConversionMode('ingame-to-real')
            setSelectedInGameCurrency(from)
            setSelectedRealCurrency(to)
            // Clear input fields when switching currencies
            setInGameAmount("")
            setTargetAmount("")
          } else {
            // Fallback to old logic for backward compatibility
            if (fromCurrency && toCurrency) {
              setConversionMode('ingame-to-ingame')
              setSelectedInGameCurrency(from)
              setSelectedTargetInGameCurrency(to)
              // Clear input fields when switching currencies
              setInGameAmount("")
              setTargetAmount("")
            } else if (fromCurrency && toRealCurrency) {
              setConversionMode('ingame-to-real')
              setSelectedInGameCurrency(from)
              setSelectedRealCurrency(to)
              // Clear input fields when switching currencies
              setInGameAmount("")
              setTargetAmount("")
            }
          }

          // Clear the stored selection after using it
          localStorage.removeItem('conversionToolSelection')
        } catch (error) {
          // If parsing fails, just ignore and use defaults
          console.warn('Failed to parse stored conversion selection:', error)
          localStorage.removeItem('conversionToolSelection')
        }
      }
    }

    // Check on initial load
    checkStoredSelection()

    // Listen for storage events (when localStorage is modified)
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'conversionToolSelection' && e.newValue) {
        checkStoredSelection()
      }
    }

    // Listen for custom event (for same-tab changes)
    const handleCustomStorageChange = () => {
      checkStoredSelection()
    }

    window.addEventListener('storage', handleStorageChange)
    window.addEventListener('conversionToolSelectionChanged', handleCustomStorageChange)

    return () => {
      window.removeEventListener('storage', handleStorageChange)
      window.removeEventListener('conversionToolSelectionChanged', handleCustomStorageChange)
    }
  }, [])

  // Fetch live exchange rates from API
  const fetchExchangeRates = async () => {
    if (exchangeRatesLoading || liveExchangeRates) return

    setExchangeRatesLoading(true)
    try {
      const response = await fetch('https://v6.exchangerate-api.com/v6/************************/latest/USD')
      if (response.ok) {
        const data: ExchangeRateResponse = await response.json()
        if (data.result === 'success') {
          setLiveExchangeRates(data.conversion_rates)
        }
      }
    } catch (error) {
      // Silently fail
    } finally {
      setExchangeRatesLoading(false)
    }
  }

  // Fetch exchange rates on component mount
  useEffect(() => {
    fetchExchangeRates()
  }, [])

  const currentInGameCurrency = IN_GAME_CURRENCIES.find(c => c.id === selectedInGameCurrency)!
  const currentTargetInGameCurrency = IN_GAME_CURRENCIES.find(c => c.id === selectedTargetInGameCurrency)!
  const currentRealCurrency = REAL_CURRENCIES.find(c => c.id === selectedRealCurrency)!
  const currentServer = SERVER_REGIONS.find(s => s.id === selectedServer)!

  // Calculate RP to EUR conversion
  const calculateRpToEur = (rp: number): number => {
    const rates = Object.entries(CONVERSION_RATES.RP_TO_EUR).sort(([a], [b]) => Number(b) - Number(a))

    // Find the appropriate tier (largest tier that the RP amount can afford)
    for (let i = 0; i < rates.length; i++) {
      const [rpTier, eurPrice] = rates[i]
      if (rp >= Number(rpTier)) {
        return (rp / Number(rpTier)) * eurPrice
      }
    }

    // For amounts smaller than the smallest tier, use the smallest tier rate
    const [smallestRp, smallestEur] = rates[rates.length - 1]
    return (rp / Number(smallestRp)) * smallestEur
  }

  // Calculate EUR to RP conversion
  const calculateEurToRp = (eur: number): number => {
    const rates = Object.entries(CONVERSION_RATES.RP_TO_EUR).sort(([a], [b]) => Number(b) - Number(a))

    for (let i = 0; i < rates.length; i++) {
      const [rpTier, eurPrice] = rates[i]
      if (eur >= eurPrice) {
        return (eur / eurPrice) * Number(rpTier) // Removed Math.floor for precision
      }
    }

    // For amounts smaller than the smallest tier, use proportional calculation
    const [smallestRp, smallestEur] = rates[rates.length - 1]
    return (eur / smallestEur) * Number(smallestRp) // Removed Math.floor for precision
  }

  // Calculate RP to any real currency conversion
  const calculateRpToRealCurrency = (rp: number, targetCurrency: string): number => {
    // Check for server-specific rates first (for USD in Latin America regions)
    let currencyKey: keyof typeof CONVERSION_RATES
    if (targetCurrency.toLowerCase() === 'usd' && selectedServer === 'las') {
      currencyKey = 'RP_TO_USD_LAS'
    } else if (targetCurrency.toLowerCase() === 'usd' && selectedServer === 'lan') {
      currencyKey = 'RP_TO_USD_LAN'
    } else {
      currencyKey = `RP_TO_${targetCurrency.toUpperCase()}` as keyof typeof CONVERSION_RATES
    }
    const rates = CONVERSION_RATES[currencyKey] as Record<string, number>

    if (rates) {
      // Use direct conversion rates
      const rateEntries = Object.entries(rates).sort(([a], [b]) => Number(b) - Number(a))

      // Find the appropriate tier (largest tier that the RP amount can afford)
      for (let i = 0; i < rateEntries.length; i++) {
        const [rpTier, price] = rateEntries[i]
        if (rp >= Number(rpTier)) {
          return (rp / Number(rpTier)) * price
        }
      }
      // For amounts smaller than the smallest tier, use the smallest tier rate
      const [smallestRp, smallestPrice] = rateEntries[rateEntries.length - 1]
      return (rp / Number(smallestRp)) * smallestPrice
    }

    // Fallback: convert through USD using exchange rates
    // First convert RP to USD using standard USD rates
    const usdAmount = calculateRpToRealCurrency(rp, 'usd')
    const exchangeRate = USD_EXCHANGE_RATES[targetCurrency.toUpperCase() as keyof typeof USD_EXCHANGE_RATES]

    if (!exchangeRate) return usdAmount // Fallback to USD if rate not found

    return usdAmount * exchangeRate
  }

  // Calculate any real currency to RP conversion
  const calculateRealCurrencyToRp = (amount: number, sourceCurrency: string): number => {
    // Check for server-specific rates first (for USD in Latin America regions)
    let currencyKey: keyof typeof CONVERSION_RATES
    if (sourceCurrency.toLowerCase() === 'usd' && selectedServer === 'las') {
      currencyKey = 'RP_TO_USD_LAS'
    } else if (sourceCurrency.toLowerCase() === 'usd' && selectedServer === 'lan') {
      currencyKey = 'RP_TO_USD_LAN'
    } else {
      currencyKey = `RP_TO_${sourceCurrency.toUpperCase()}` as keyof typeof CONVERSION_RATES
    }
    const rates = CONVERSION_RATES[currencyKey] as Record<string, number>

    if (rates) {
      // Use direct conversion rates
      const rateEntries = Object.entries(rates).sort(([a], [b]) => Number(b) - Number(a))

      for (let i = 0; i < rateEntries.length; i++) {
        const [rpTier, price] = rateEntries[i]
        if (amount >= price) {
          return (amount / price) * Number(rpTier)
        }
      }
      // For amounts smaller than the smallest tier, use the smallest tier rate
      const [smallestRp, smallestPrice] = rateEntries[rateEntries.length - 1]
      return (amount / smallestPrice) * Number(smallestRp)
    }

    // Fallback: convert through USD using exchange rates
    const exchangeRate = USD_EXCHANGE_RATES[sourceCurrency.toUpperCase() as keyof typeof USD_EXCHANGE_RATES]

    if (!exchangeRate) {
      // If no exchange rate found, use EUR as final fallback
      return calculateEurToRp(amount)
    }

    const usdAmount = amount / exchangeRate
    // Use the standard USD conversion rates
    const usdRates = CONVERSION_RATES.RP_TO_USD as Record<string, number>
    const rateEntries = Object.entries(usdRates).sort(([a], [b]) => Number(b) - Number(a))

    for (let i = 0; i < rateEntries.length; i++) {
      const [rpTier, price] = rateEntries[i]
      if (usdAmount >= price) {
        return (usdAmount / price) * Number(rpTier)
      }
    }
    // For amounts smaller than the smallest tier, use the smallest tier rate
    const [smallestRp, smallestPrice] = rateEntries[rateEntries.length - 1]
    return (usdAmount / smallestPrice) * Number(smallestRp)
  }

  // Helper function to convert any in-game currency to RP
  const convertToRp = (amount: number, fromCurrency: string): number => {
    switch (fromCurrency) {
      case 'rp':
        return amount
      case 'as':
        return amount * CONVERSION_RATES.AS_TO_RP
      case 'me':
        return amount * CONVERSION_RATES.ME_TO_RP
      case 'be':
        return amount / CONVERSION_RATES.RP_TO_BE
      case 'oe':
        return amount / CONVERSION_RATES.RP_TO_OE
      default:
        return 0
    }
  }

  // Helper function to convert RP to any in-game currency
  const convertFromRp = (rpAmount: number, toCurrency: string): number => {
    switch (toCurrency) {
      case 'rp':
        return rpAmount
      case 'as':
        return rpAmount / CONVERSION_RATES.AS_TO_RP
      case 'me':
        return rpAmount / CONVERSION_RATES.ME_TO_RP
      case 'be':
        return rpAmount * CONVERSION_RATES.RP_TO_BE
      case 'oe':
        return rpAmount * CONVERSION_RATES.RP_TO_OE
      default:
        return 0
    }
  }

  // Helper function to convert between any two in-game currencies
  const convertInGameCurrency = (amount: number, fromCurrency: string, toCurrency: string): number => {
    if (fromCurrency === toCurrency) return amount
    const rpAmount = convertToRp(amount, fromCurrency)
    return convertFromRp(rpAmount, toCurrency)
  }

  // Function to find the best value combination of RP packages
  const getOptimalRpPackageCombination = (targetRp: number): { cost: number; packages: RpPackage[]; totalRp: number; value: number } | null => {
    if (!targetRp || targetRp <= 0) return null

    // Get the conversion rates for the selected currency and server
    let currencyKey: keyof typeof CONVERSION_RATES
    if (selectedRealCurrency.toLowerCase() === 'usd' && selectedServer === 'las') {
      currencyKey = 'RP_TO_USD_LAS'
    } else if (selectedRealCurrency.toLowerCase() === 'usd' && selectedServer === 'lan') {
      currencyKey = 'RP_TO_USD_LAN'
    } else {
      currencyKey = `RP_TO_${selectedRealCurrency.toUpperCase()}` as keyof typeof CONVERSION_RATES
    }
    const rates = CONVERSION_RATES[currencyKey] as Record<string, number>

    // Create packages with current currency prices
    let packagesWithCurrentCurrency: RpPackage[]

    if (rates) {
      // Use hardcoded rates if available
      packagesWithCurrentCurrency = RP_PACKAGES.map(pkg => ({
        ...pkg,
        price: rates[pkg.rp.toString()] || pkg.price,
        efficiency: pkg.rp / (rates[pkg.rp.toString()] || pkg.price)
      }))
    } else if (liveExchangeRates) {
      // Use USD prices converted via live exchange rates for unsupported currencies
      const exchangeRate = liveExchangeRates[selectedRealCurrency.toUpperCase()]
      if (!exchangeRate) return null

      const usdRates = CONVERSION_RATES.RP_TO_USD as Record<string, number>
      packagesWithCurrentCurrency = RP_PACKAGES.map(pkg => {
        const usdPrice = usdRates[pkg.rp.toString()] || pkg.price
        const convertedPrice = usdPrice * exchangeRate
        return {
          ...pkg,
          price: convertedPrice,
          efficiency: pkg.rp / convertedPrice
        }
      })
    } else {
      // No rates available
      return null
    }

    const solutions: { cost: number; packages: RpPackage[]; totalRp: number; excessPercentage: number }[] = []

    // Generate combinations using a more controlled approach
    const findCombinations = (remaining: number, currentCost: number, currentPackages: RpPackage[], packageIndex: number) => {
      if (remaining <= 0) {
        const totalRp = currentPackages.reduce((sum, pkg) => sum + pkg.rp, 0)
        const excessRp = totalRp - targetRp
        const excessPercentage = excessRp / targetRp

        // Only consider solutions with reasonable excess (< 50%)
        if (excessPercentage < 0.5) {
          solutions.push({
            cost: currentCost,
            packages: [...currentPackages],
            totalRp,
            excessPercentage
          })
        }
        return
      }

      // Try each package type
      for (let i = packageIndex; i < packagesWithCurrentCurrency.length; i++) {
        const pkg = packagesWithCurrentCurrency[i]

        // Limit the number of same packages to avoid excessive combinations
        const maxSamePackage = Math.min(5, Math.ceil(targetRp / pkg.rp) + 1)

        for (let count = 1; count <= maxSamePackage; count++) {
          const totalCost = currentCost + (pkg.price * count)
          const totalRpFromThisPackage = pkg.rp * count

          // Skip if this would be too expensive or create too much excess
          if (totalCost > targetRp * 0.08) break // Rough limit: $0.08 per RP

          const newPackages = [...currentPackages, ...Array(count).fill(pkg)]
          findCombinations(remaining - totalRpFromThisPackage, totalCost, newPackages, i + 1)
        }
      }
    }

    findCombinations(targetRp, 0, [], 0)

    if (solutions.length === 0) return null

    // Find the best solution: prioritize lower cost, but accept small cost increase for much less excess
    let bestSolution = solutions[0]

    for (const solution of solutions) {
      const currentIsBetter =
        solution.cost < bestSolution.cost || // Cheaper
        (solution.cost <= bestSolution.cost * 1.05 && solution.excessPercentage < bestSolution.excessPercentage * 0.8) // Slightly more expensive but much less excess

      if (currentIsBetter) {
        bestSolution = solution
      }
    }

    return {
      cost: bestSolution.cost,
      packages: bestSolution.packages,
      totalRp: bestSolution.totalRp,
      value: bestSolution.cost
    }
  }

  // Get conversion rate info
  const getConversionInfo = (): ConversionInfo => {
    if (conversionMode === 'ingame-to-real') {
      if (currentRealCurrency.supported && currentServer.supported) {
        let rateDescription = ''
        if (selectedInGameCurrency === 'rp') {
          rateDescription = `Based on official server pricing`
        } else if (selectedInGameCurrency === 'as') {
          rateDescription = `1 AS = 400 RP → ${currentRealCurrency.name}`
        } else if (selectedInGameCurrency === 'me') {
          rateDescription = `1 ME = 69.5 RP → ${currentRealCurrency.name}`
        } else if (selectedInGameCurrency === 'be') {
          rateDescription = `14.5 BE = 1 RP → ${currentRealCurrency.name}`
        } else if (selectedInGameCurrency === 'oe') {
          rateDescription = `0.67 OE = 1 RP → ${currentRealCurrency.name}`
        }

        return {
          rate: rateDescription,
          bidirectional: true, // All ingame-to-real conversions are bidirectional via RP
          supported: true
        }
      } else {
        return {
          rate: `${currentRealCurrency.name} not yet supported`,
          bidirectional: false,
          supported: false
        }
      }
    } else {
      // In-game to in-game conversions
      // Generate conversion rate description for any currency pair
      const generateConversionRate = (fromCurrency: string, toCurrency: string): string => {
        const currencyNames = {
          'rp': 'RP',
          'as': 'Ancient Spark',
          'me': 'Mythic Essence',
          'be': 'Blue Essence',
          'oe': 'Orange Essence'
        }

        const fromName = currencyNames[fromCurrency as keyof typeof currencyNames]
        const toName = currencyNames[toCurrency as keyof typeof currencyNames]

        // Calculate the conversion rate
        const testAmount = 1
        const convertedAmount = convertInGameCurrency(testAmount, fromCurrency, toCurrency)

        if (convertedAmount >= 1) {
          return `1 ${fromName} = ${convertedAmount.toFixed(convertedAmount >= 10 ? 1 : 3)} ${toName}`
        } else {
          // Show inverse rate for better readability
          const inverseAmount = 1 / convertedAmount
          return `${inverseAmount.toFixed(inverseAmount >= 10 ? 1 : 3)} ${fromName} = 1 ${toName}`
        }
      }

      // Support all currency combinations except same currency
      if (selectedInGameCurrency === selectedTargetInGameCurrency) {
        return {
          rate: 'Same currency selected',
          bidirectional: false,
          supported: false,
          isSameCurrency: true
        }
      } else {
        // All other combinations are now supported via RP conversion
        return {
          rate: generateConversionRate(selectedInGameCurrency, selectedTargetInGameCurrency),
          bidirectional: true,
          supported: true
        }
      }
    }
  }

  const conversionInfo = getConversionInfo()

  // Handle in-game currency amount change
  const handleInGameAmountChange = (value: string) => {
    setInGameAmount(value)
    if (!value || isNaN(Number(value)) || !conversionInfo.supported) {
      setTargetAmount("")
      return
    }

    const numValue = Number(value)
    let result = 0

    if (conversionMode === 'ingame-to-real') {
      // Convert any in-game currency to RP first, then to real currency
      let rpAmount = 0

      if (selectedInGameCurrency === 'rp') {
        rpAmount = numValue
      } else if (selectedInGameCurrency === 'as') {
        rpAmount = numValue * CONVERSION_RATES.AS_TO_RP
      } else if (selectedInGameCurrency === 'me') {
        rpAmount = numValue * CONVERSION_RATES.ME_TO_RP
      } else if (selectedInGameCurrency === 'be') {
        rpAmount = numValue / CONVERSION_RATES.RP_TO_BE
      } else if (selectedInGameCurrency === 'oe') {
        rpAmount = numValue / CONVERSION_RATES.RP_TO_OE
      }

      // Now convert RP to the selected real currency
      result = calculateRpToRealCurrency(rpAmount, selectedRealCurrency)
      setTargetAmount(result.toFixed(2))
    } else {
      // In-game to in-game conversions - now supports all combinations
      result = convertInGameCurrency(numValue, selectedInGameCurrency, selectedTargetInGameCurrency)

      // Format the result based on the target currency
      if (selectedTargetInGameCurrency === 'be' || selectedTargetInGameCurrency === 'oe') {
        // Round to whole numbers for BE and OE
        setTargetAmount(Math.round(result).toString())
      } else if (selectedTargetInGameCurrency === 'as') {
        // Show more precision for Ancient Sparks since they're rare
        setTargetAmount(result.toFixed(4))
      } else if (selectedTargetInGameCurrency === 'me') {
        // Show 3 decimal places for Mythic Essence
        setTargetAmount(result.toFixed(3))
      } else {
        // Default formatting for RP and others
        setTargetAmount(result.toFixed(1))
      }
    }
  }

  // Handle target amount change (only for bidirectional)
  const handleTargetAmountChange = (value: string) => {
    if (!conversionInfo.bidirectional || !conversionInfo.supported) return

    setTargetAmount(value)
    if (!value || isNaN(Number(value))) {
      setInGameAmount("")
      return
    }

    const numValue = Number(value)

    if (conversionMode === 'ingame-to-real') {
      // Convert real currency to RP first, then to the selected in-game currency
      const rpAmount = calculateRealCurrencyToRp(numValue, selectedRealCurrency)

      // Now convert RP to the selected in-game currency
      const result = convertFromRp(rpAmount, selectedInGameCurrency)

      // Format the result based on the source currency
      if (selectedInGameCurrency === 'be' || selectedInGameCurrency === 'oe') {
        // Round to whole numbers for BE and OE
        setInGameAmount(Math.round(result).toString())
      } else if (selectedInGameCurrency === 'as') {
        // Show more precision for Ancient Sparks since they're rare
        setInGameAmount(result.toFixed(4))
      } else if (selectedInGameCurrency === 'me') {
        // Show 3 decimal places for Mythic Essence
        setInGameAmount(result.toFixed(3))
      } else {
        // Default formatting for RP and others
        setInGameAmount(result.toFixed(1))
      }
    } else {
      // Reverse in-game to in-game conversions - now supports all combinations
      const result = convertInGameCurrency(numValue, selectedTargetInGameCurrency, selectedInGameCurrency)

      // Format the result based on the source currency
      if (selectedInGameCurrency === 'be' || selectedInGameCurrency === 'oe') {
        // Round to whole numbers for BE and OE
        setInGameAmount(Math.round(result).toString())
      } else if (selectedInGameCurrency === 'as') {
        // Show more precision for Ancient Sparks since they're rare
        setInGameAmount(result.toFixed(4))
      } else if (selectedInGameCurrency === 'me') {
        // Show 3 decimal places for Mythic Essence
        setInGameAmount(result.toFixed(3))
      } else {
        // Default formatting for RP and others
        setInGameAmount(result.toFixed(1))
      }
    }
  }

  // Handle dropdown changes
  const handleInGameCurrencyChange = (currencyId: string) => {
    setSelectedInGameCurrency(currencyId)
    setInGameAmount("")
    setTargetAmount("")
    setOpenInGame(false)
  }

  const handleTargetInGameCurrencyChange = (currencyId: string) => {
    setSelectedTargetInGameCurrency(currencyId)
    setInGameAmount("")
    setTargetAmount("")
    setOpenTargetInGame(false)
  }

  const handleRealCurrencyChange = (currencyId: string) => {
    const currency = REAL_CURRENCIES.find(c => c.id === currencyId)
    if (currency?.supported) {
      setSelectedRealCurrency(currencyId)
      setInGameAmount("")
      setTargetAmount("")

      // Auto-switch server based on currency
      if (currencyId === 'usd' && selectedServer !== 'na' && selectedServer !== 'me' && selectedServer !== 'las' && selectedServer !== 'lan' && selectedServer !== 'tw' && selectedServer !== 'sea') {
        setSelectedServer('na') // Default to NA for USD
      } else if (currencyId === 'aud' && selectedServer !== 'oce') {
        setSelectedServer('oce')
      } else if (currencyId === 'brl' && selectedServer !== 'br') {
        setSelectedServer('br')
      } else if (currencyId === 'try' && selectedServer !== 'tr') {
        setSelectedServer('tr')
      } else if (currencyId === 'jpy' && selectedServer !== 'jp') {
        setSelectedServer('jp')
      } else if (currencyId === 'rub' && selectedServer !== 'ru') {
        setSelectedServer('ru')
      } else if (currencyId === 'gbp' && selectedServer !== 'euw') {
        setSelectedServer('euw')
      } else if (currencyId === 'eur' && (selectedServer === 'na' || selectedServer === 'me' || selectedServer === 'oce' || selectedServer === 'br' || selectedServer === 'tr' || selectedServer === 'jp' || selectedServer === 'ru' || selectedServer === 'las' || selectedServer === 'lan' || selectedServer === 'tw' || selectedServer === 'sea')) {
        setSelectedServer('euw')
      }
    }
    setOpenReal(false)
  }

  const handleServerChange = (serverId: string) => {
    const server = SERVER_REGIONS.find(s => s.id === serverId)
    if (server?.supported) {
      setSelectedServer(serverId)
      setInGameAmount("")
      setTargetAmount("")

      // Auto-switch currency based on server
      if (serverId === 'na' && selectedRealCurrency !== 'usd') {
        setSelectedRealCurrency('usd')
      } else if (serverId === 'me' && selectedRealCurrency !== 'usd') {
        setSelectedRealCurrency('usd')
      } else if (serverId === 'oce' && selectedRealCurrency !== 'aud') {
        setSelectedRealCurrency('aud')
      } else if (serverId === 'br' && selectedRealCurrency !== 'brl') {
        setSelectedRealCurrency('brl')
      } else if (serverId === 'tr' && selectedRealCurrency !== 'try') {
        setSelectedRealCurrency('try')
      } else if (serverId === 'jp' && selectedRealCurrency !== 'jpy') {
        setSelectedRealCurrency('jpy')
      } else if (serverId === 'ru' && selectedRealCurrency !== 'rub') {
        setSelectedRealCurrency('rub')
      } else if (serverId === 'las' && selectedRealCurrency !== 'usd') {
        setSelectedRealCurrency('usd')
      } else if (serverId === 'lan' && selectedRealCurrency !== 'usd') {
        setSelectedRealCurrency('usd')
      } else if (serverId === 'tw' && selectedRealCurrency !== 'usd') {
        setSelectedRealCurrency('usd')
      } else if (serverId === 'sea' && selectedRealCurrency !== 'usd') {
        setSelectedRealCurrency('usd')
      } else if (serverId === 'euw' && (selectedRealCurrency === 'usd' || selectedRealCurrency === 'aud' || selectedRealCurrency === 'brl' || selectedRealCurrency === 'try' || selectedRealCurrency === 'jpy' || selectedRealCurrency === 'rub')) {
        setSelectedRealCurrency('eur') // Default to EUR for EUW, but allow GBP
      } else if (serverId === 'eune' && (selectedRealCurrency === 'usd' || selectedRealCurrency === 'aud' || selectedRealCurrency === 'brl' || selectedRealCurrency === 'try' || selectedRealCurrency === 'jpy' || selectedRealCurrency === 'rub' || selectedRealCurrency === 'gbp')) {
        setSelectedRealCurrency('eur') // EUNE only supports EUR
      }
    }
    setOpenServer(false)
  }

  const handleModeChange = (mode: ConversionMode) => {
    setConversionMode(mode)
    setInGameAmount("")
    setTargetAmount("")
  }



  return (
    <ConversionBackground customImageUrl="/images/SkinSalesCards.jpg">
      <SharedLayout>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8 relative z-20">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-white mb-4">
              Currency Conversion Tool
            </h1>
            <p className="text-gray-300 text-lg">
              Convert between League of Legends currencies and real money
            </p>
            <Badge variant="secondary" className="mt-2 bg-orange-600/20 text-orange-300 border-orange-600/30">
              Live Rates
            </Badge>
          </div>

          {/* Conversion Mode Toggle */}
          <div className="max-w-md mx-auto mb-8">
            <div className="bg-gray-900/60 border border-orange-700/30 rounded-lg p-1 backdrop-blur-sm relative">
              {/* Animated Background Slider */}
              <div
                className={cn(
                  "absolute top-1 bottom-1 w-[calc(50%-2px)] bg-orange-600 rounded-md shadow-lg transition-all duration-300 ease-in-out",
                  conversionMode === 'ingame-to-real' ? "left-1" : "left-[calc(50%+1px)]"
                )}
              />

              <div className="grid grid-cols-2 gap-1 relative z-10">
                <button
                  onClick={() => handleModeChange('ingame-to-real')}
                  className={cn(
                    "px-4 py-3 rounded-md text-sm font-medium transition-all duration-300",
                    conversionMode === 'ingame-to-real'
                      ? "text-white"
                      : "text-gray-300 hover:text-white"
                  )}
                >
                  <div className="flex items-center justify-center gap-2">
                    <Image
                      src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/rp.svg"
                      alt="RP"
                      width={16}
                      height={16}
                    />
                    <ArrowLeftRight className="h-4 w-4" />
                    <span className="text-sm font-bold">{currentRealCurrency.code}</span>
                  </div>
                  <div className="mt-1">Real Currency</div>
                </button>
                <button
                  onClick={() => handleModeChange('ingame-to-ingame')}
                  className={cn(
                    "px-4 py-3 rounded-md text-sm font-medium transition-all duration-300",
                    conversionMode === 'ingame-to-ingame'
                      ? "text-white"
                      : "text-gray-300 hover:text-white"
                  )}
                >
                  <div className="flex items-center justify-center gap-2">
                    <Image
                      src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/rp.svg"
                      alt="RP"
                      width={16}
                      height={16}
                    />
                    <ArrowLeftRight className="h-4 w-4" />
                    <Image
                      src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/currency/icons/ancient_spark.svg"
                      alt="AS"
                      width={16}
                      height={16}
                    />
                  </div>
                  <div className="mt-1">In-Game Currency</div>
                </button>
              </div>
            </div>
          </div>

          {/* Currency Selection Dropdowns */}
          <div className="max-w-4xl mx-auto mb-8">
            <div className={cn(
              "grid gap-4",
              conversionMode === 'ingame-to-real'
                ? "grid-cols-1 md:grid-cols-3"
                : "grid-cols-1 md:grid-cols-2"
            )}>

              {/* In-Game Currency Dropdown */}
              <div className="space-y-2">
                <Label className="text-white text-sm font-medium">In-Game Currency</Label>
                <Popover open={openInGame} onOpenChange={setOpenInGame}>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      role="combobox"
                      aria-expanded={openInGame}
                      className="w-full justify-between bg-gray-900/60 border-orange-700/30 text-white hover:bg-gray-800/50 h-[40px]"
                    >
                      <div className="flex items-center gap-2">
                        <Image
                          src={currentInGameCurrency.icon}
                          alt={currentInGameCurrency.name}
                          width={16}
                          height={16}
                        />
                        <span className="truncate">{currentInGameCurrency.name}</span>
                      </div>
                      <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0 bg-gray-900 border-gray-700">
                    <Command>
                      {!isMobile && (
                        <CommandInput
                          placeholder="Search currencies..."
                          className="h-9 bg-gray-900 border-gray-700 text-white placeholder-gray-400"
                        />
                      )}
                      <CommandList className="max-h-[300px]">
                        <CommandEmpty className="text-gray-400 py-6 text-center text-sm">
                          No currency found.
                        </CommandEmpty>
                        <CommandGroup>
                          {IN_GAME_CURRENCIES.map((currency) => (
                            <CommandItem
                              key={currency.id}
                              value={currency.id}
                              onSelect={() => handleInGameCurrencyChange(currency.id)}
                              className="text-white hover:bg-orange-400/10 cursor-pointer"
                            >
                              <div className="flex items-center gap-2 flex-1">
                                <Image
                                  src={currency.icon}
                                  alt={currency.name}
                                  width={16}
                                  height={16}
                                />
                                <span>{currency.name}</span>
                              </div>
                              <Check
                                className={cn(
                                  "ml-auto h-4 w-4",
                                  selectedInGameCurrency === currency.id ? "opacity-100" : "opacity-0"
                                )}
                              />
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>
              </div>

              {/* Server Region Dropdown (only for ingame-to-real mode) */}
              {conversionMode === 'ingame-to-real' && (
              <div className="space-y-2">
                <Label className="text-white text-sm font-medium">Server Region</Label>
                <Popover open={openServer} onOpenChange={setOpenServer}>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      role="combobox"
                      aria-expanded={openServer}
                      className="w-full justify-between bg-gray-900/60 border-orange-700/30 text-white hover:bg-gray-800/50 h-[40px]"
                    >
                      <span className="truncate">{currentServer.name}</span>
                      <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0 bg-gray-900 border-gray-700">
                    <Command>
                      {!isMobile && (
                        <CommandInput
                          placeholder="Search servers..."
                          className="h-9 bg-gray-900 border-gray-700 text-white placeholder-gray-400"
                        />
                      )}
                      <CommandList className="max-h-[300px]">
                        <CommandEmpty className="text-gray-400 py-6 text-center text-sm">
                          No server found.
                        </CommandEmpty>
                        <CommandGroup>
                          {SERVER_REGIONS.map((server) => (
                            <CommandItem
                              key={server.id}
                              value={server.id}
                              onSelect={() => handleServerChange(server.id)}
                              className={cn(
                                "cursor-pointer",
                                server.supported
                                  ? "text-white hover:bg-green-400/10"
                                  : "text-gray-500 cursor-not-allowed opacity-50"
                              )}
                              disabled={!server.supported}
                            >
                              <div className="flex items-center justify-between w-full">
                                <span>{server.name}</span>
                                {!server.supported && (
                                  <span className="text-xs text-gray-500">Coming Soon</span>
                                )}
                                {server.supported && (
                                  <Check
                                    className={cn(
                                      "h-4 w-4",
                                      selectedServer === server.id ? "opacity-100" : "opacity-0"
                                    )}
                                  />
                                )}
                              </div>
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>
              </div>
              )}

              {/* Real Currency Dropdown (only for ingame-to-real mode) */}
              {conversionMode === 'ingame-to-real' && (
              <div className="space-y-2">
                <Label className="text-white text-sm font-medium">Real Currency</Label>
                <Popover open={openReal} onOpenChange={setOpenReal}>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      role="combobox"
                      aria-expanded={openReal}
                      className="w-full justify-between bg-gray-900/60 border-orange-700/30 text-white hover:bg-gray-800/50 h-[40px]"
                    >
                      <div className="flex items-center gap-2">
                        {currentRealCurrency.supported ? (
                          <span className="text-blue-400 text-xs font-bold w-8 text-center">{currentRealCurrency.code}</span>
                        ) : (
                          <div className="w-8 h-4" /> // Empty space for alignment
                        )}
                        <span className="truncate">{currentRealCurrency.name}</span>
                      </div>
                      <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0 bg-gray-900 border-gray-700">
                    <Command>
                      {!isMobile && (
                        <CommandInput
                          placeholder="Search currencies..."
                          className="h-9 bg-gray-900 border-gray-700 text-white placeholder-gray-400"
                        />
                      )}
                      <CommandList className="max-h-[300px]">
                        <CommandEmpty className="text-gray-400 py-6 text-center text-sm">
                          No currency found.
                        </CommandEmpty>
                        <CommandGroup>
                          {REAL_CURRENCIES.map((currency) => (
                            <CommandItem
                              key={currency.id}
                              value={currency.id}
                              onSelect={() => handleRealCurrencyChange(currency.id)}
                              className={cn(
                                "cursor-pointer",
                                currency.supported
                                  ? "text-white hover:bg-blue-400/10"
                                  : "text-gray-500 cursor-not-allowed opacity-50"
                              )}
                              disabled={!currency.supported}
                            >
                              <div className="flex items-center gap-2 flex-1">
                                {currency.supported ? (
                                  <span className="text-blue-400 text-xs font-bold w-8 text-center">{currency.code}</span>
                                ) : (
                                  <div className="w-8 h-4" /> // Empty space for alignment
                                )}
                                <span>{currency.name}</span>
                                {!currency.supported && (
                                  <span className="text-xs text-gray-500 ml-auto">Coming Soon</span>
                                )}
                              </div>
                              {currency.supported && (
                                <Check
                                  className={cn(
                                    "ml-auto h-4 w-4",
                                    selectedRealCurrency === currency.id ? "opacity-100" : "opacity-0"
                                  )}
                                />
                              )}
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>
              </div>
              )}

              {/* Target In-Game Currency Dropdown (only for ingame-to-ingame mode) */}
              {conversionMode === 'ingame-to-ingame' && (
                <div className="space-y-2">
                  <Label className="text-white text-sm font-medium">Target Currency</Label>
                  <Popover open={openTargetInGame} onOpenChange={setOpenTargetInGame}>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        role="combobox"
                        aria-expanded={openTargetInGame}
                        className="w-full justify-between bg-gray-900/60 border-orange-700/30 text-white hover:bg-gray-800/50 h-[40px]"
                      >
                        <div className="flex items-center gap-2">
                          <Image
                            src={currentTargetInGameCurrency.icon}
                            alt={currentTargetInGameCurrency.name}
                            width={16}
                            height={16}
                          />
                          <span className="truncate">{currentTargetInGameCurrency.name}</span>
                        </div>
                        <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0 bg-gray-900 border-gray-700">
                      <Command>
                        {!isMobile && (
                          <CommandInput
                            placeholder="Search currencies..."
                            className="h-9 bg-gray-900 border-gray-700 text-white placeholder-gray-400"
                          />
                        )}
                        <CommandList className="max-h-[300px]">
                          <CommandEmpty className="text-gray-400 py-6 text-center text-sm">
                            No currency found.
                          </CommandEmpty>
                          <CommandGroup>
                            {IN_GAME_CURRENCIES.map((currency) => (
                              <CommandItem
                                key={currency.id}
                                value={currency.id}
                                onSelect={() => handleTargetInGameCurrencyChange(currency.id)}
                                className="text-white hover:bg-orange-400/10 cursor-pointer"
                              >
                                <div className="flex items-center gap-2 flex-1">
                                  <Image
                                    src={currency.icon}
                                    alt={currency.name}
                                    width={16}
                                    height={16}
                                  />
                                  <span>{currency.name}</span>
                                </div>
                                <Check
                                  className={cn(
                                    "ml-auto h-4 w-4",
                                    selectedTargetInGameCurrency === currency.id ? "opacity-100" : "opacity-0"
                                  )}
                                />
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>
                </div>
              )}

            </div>
          </div>

          {/* Main Content Grid */}
          <div className="max-w-4xl mx-auto">
            <div className={`grid grid-cols-1 gap-6 ${
              conversionMode === 'ingame-to-real' ? 'lg:grid-cols-4' : ''
            }`}>
              {/* Main Conversion Card */}
              <div className={conversionMode === 'ingame-to-real' ? 'lg:col-span-3' : ''}>
              <Card className={cn(
                "bg-gray-900/60 border-orange-700/30 backdrop-blur-sm",
                !conversionInfo.supported && "opacity-60"
              )}>
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-3">
                  <div className="flex items-center gap-2">
                    <Image
                      src={currentInGameCurrency.icon}
                      alt={currentInGameCurrency.name}
                      width={24}
                      height={24}
                    />
                    <ArrowLeftRight className="h-5 w-5 text-orange-400" />
                    {conversionMode === 'ingame-to-real' ? (
                      currentRealCurrency.supported ? (
                        <span className="text-blue-400 text-lg font-bold">{currentRealCurrency.code}</span>
                      ) : (
                        <span className="text-gray-400 text-lg font-bold">{currentRealCurrency.code}</span>
                      )
                    ) : (
                      <Image
                        src={currentTargetInGameCurrency.icon}
                        alt={currentTargetInGameCurrency.name}
                        width={24}
                        height={24}
                      />
                    )}
                  </div>
                  {currentInGameCurrency.name} {conversionInfo.bidirectional ? 'to' : '→'} {
                    conversionMode === 'ingame-to-real' ? currentRealCurrency.name : currentTargetInGameCurrency.name
                  }
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {!conversionInfo.supported && (
                  <div className="bg-amber-900/20 border border-amber-700/30 rounded-lg p-3 mb-4">
                    <p className="text-amber-300 text-sm flex items-center gap-2">
                      <Info className="h-4 w-4" />
                      {conversionInfo.isSameCurrency
                        ? SAME_CURRENCY_JOKES[Math.floor(Math.random() * SAME_CURRENCY_JOKES.length)]
                        : "This conversion combination is not yet supported. Coming soon!"
                      }
                    </p>
                  </div>
                )}
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="ingame-input" className="text-gray-300 flex items-center gap-2">
                      <Image
                        src={currentInGameCurrency.icon}
                        alt={currentInGameCurrency.name}
                        width={16}
                        height={16}
                      />
                      {currentInGameCurrency.name}
                    </Label>
                    <Input
                      id="ingame-input"
                      type="number"
                      step="1"
                      placeholder={currentInGameCurrency.placeholder}
                      value={inGameAmount}
                      onChange={(e) => handleInGameAmountChange(e.target.value)}
                      disabled={!conversionInfo.supported}
                      className="bg-gray-800/50 border-gray-700/50 text-white placeholder-gray-400 disabled:opacity-50 [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none [-moz-appearance:textfield]"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="target-input" className="text-gray-300 flex items-center gap-2">
                      {conversionMode === 'ingame-to-real' ? (
                        <>
                          {currentRealCurrency.supported ? (
                            <span className="text-blue-400 text-xs font-bold w-8 text-center">{currentRealCurrency.code}</span>
                          ) : (
                            <span className="text-gray-400 text-xs w-8 text-center">{currentRealCurrency.code}</span>
                          )}
                          {currentRealCurrency.name}
                        </>
                      ) : (
                        <>
                          <Image
                            src={currentTargetInGameCurrency.icon}
                            alt={currentTargetInGameCurrency.name}
                            width={16}
                            height={16}
                          />
                          {currentTargetInGameCurrency.name}
                        </>
                      )}
                    </Label>
                    <Input
                      id="target-input"
                      type="number"
                      step={conversionMode === 'ingame-to-real' ? "0.01" : "1"}
                      placeholder={
                        conversionMode === 'ingame-to-real'
                          ? `Enter ${currentRealCurrency.name} amount`
                          : currentTargetInGameCurrency.placeholder
                      }
                      value={targetAmount}
                      onChange={(e) => handleTargetAmountChange(e.target.value)}
                      readOnly={!conversionInfo.bidirectional}
                      disabled={!conversionInfo.supported}
                      className={cn(
                        "placeholder-gray-400 disabled:opacity-50 [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none [-moz-appearance:textfield]",
                        conversionInfo.bidirectional && conversionInfo.supported
                          ? "bg-gray-800/50 border-gray-700/50 text-white"
                          : "bg-gray-800/30 border-gray-700/30 text-gray-300"
                      )}
                    />
                  </div>
                </div>
                <div className="text-sm text-gray-400 text-center">
                  {conversionInfo.rate}
                </div>
              </CardContent>
            </Card>
            </div>

            {/* RP Package Recommendation - Small Menu Card (Only in Real Currency tab) */}
            {conversionMode === 'ingame-to-real' && (
              <div className="lg:col-span-1">
                <Card className="bg-gray-900/60 border-orange-700/30 backdrop-blur-sm h-full flex flex-col">
                <CardHeader className="pb-3">
                  <CardTitle className="text-white text-sm flex items-center gap-2">
                    Recommendations
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-0 flex-1 flex flex-col justify-center">
                  {conversionMode === 'ingame-to-real' && selectedInGameCurrency === 'rp' && inGameAmount && !isNaN(Number(inGameAmount)) && Number(inGameAmount) > 0 ? (
                    (() => {
                      // Show loading state if exchange rates are being fetched
                      if (exchangeRatesLoading) {
                        return (
                          <div className="text-center text-gray-400 text-xs py-4">
                            Loading recommendations...
                          </div>
                        )
                      }

                      const optimalCombination = getOptimalRpPackageCombination(Number(inGameAmount))
                      if (!optimalCombination || optimalCombination.packages.length === 0) {
                        return (
                          <div className="text-center text-gray-400 text-xs py-4">
                            {exchangeRatesLoading ? "Loading recommendations..." : "Unable to calculate"}
                          </div>
                        )
                      }

                      const totalRp = optimalCombination.totalRp
                      const totalCost = optimalCombination.cost
                      const targetRp = Number(inGameAmount)
                      const excessRp = totalRp - targetRp

                      // Group identical packages for cleaner display
                      const packageCounts = optimalCombination.packages.reduce((acc, pkg) => {
                        const key = `${pkg.rp}-${pkg.price}`
                        acc[key] = (acc[key] || { ...pkg, count: 0 })
                        acc[key].count++
                        return acc
                      }, {} as Record<string, RpPackage & { count: number }>)

                      const groupedPackages = Object.values(packageCounts)

                      return (
                        <div className="space-y-3">
                          {/* Package combination display - fixed 2-column layout */}
                          <div className="space-y-2">
                            {groupedPackages.length === 1 ? (
                              /* Single package - centered */
                              <div className="flex flex-col items-center gap-2">
                                <div className="relative">
                                  <Image
                                    src={groupedPackages[0].image}
                                    alt={`${groupedPackages[0].rp} RP Package`}
                                    width={60}
                                    height={60}
                                    className="rounded-lg shadow-lg"
                                  />
                                  {groupedPackages[0].count > 1 && (
                                    <div className="absolute -top-2 -right-2 bg-black/70 backdrop-blur-sm border border-orange-400/50 text-orange-300 text-xs font-bold rounded-full w-7 h-7 flex items-center justify-center shadow-lg">
                                      {groupedPackages[0].count}
                                    </div>
                                  )}
                                </div>
                                <div className="flex items-center gap-1">
                                  <Image
                                    src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/rp.svg"
                                    alt="RP"
                                    width={16}
                                    height={16}
                                  />
                                  <span className="text-orange-400 font-medium text-sm">
                                    {groupedPackages[0].rp.toLocaleString()}
                                  </span>
                                </div>
                              </div>
                            ) : (
                              /* Two packages - side by side with plus sign between */
                              <div className="space-y-2">
                                {/* Images row with plus sign between */}
                                <div className="flex items-center justify-center gap-3">
                                  <div className="flex flex-col items-center gap-2">
                                    <div className="relative">
                                      <Image
                                        src={groupedPackages[0].image}
                                        alt={`${groupedPackages[0].rp} RP Package`}
                                        width={60}
                                        height={60}
                                        className="rounded-lg shadow-lg"
                                      />
                                      {groupedPackages[0].count > 1 && (
                                        <div className="absolute -top-2 -right-2 bg-black/70 backdrop-blur-sm border border-orange-400/50 text-orange-300 text-xs font-bold rounded-full w-7 h-7 flex items-center justify-center shadow-lg">
                                          {groupedPackages[0].count}
                                        </div>
                                      )}
                                    </div>
                                    <div className="flex items-center gap-1">
                                      <Image
                                        src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/rp.svg"
                                        alt="RP"
                                        width={16}
                                        height={16}
                                      />
                                      <span className="text-orange-400 font-medium text-sm">
                                        {groupedPackages[0].rp.toLocaleString()}
                                      </span>
                                    </div>
                                  </div>

                                  {/* Plus sign between packages */}
                                  <div className="flex items-center">
                                    <span className="text-orange-400 text-lg font-bold">+</span>
                                  </div>

                                  <div className="flex flex-col items-center gap-2">
                                    <div className="relative">
                                      <Image
                                        src={groupedPackages[1].image}
                                        alt={`${groupedPackages[1].rp} RP Package`}
                                        width={60}
                                        height={60}
                                        className="rounded-lg shadow-lg"
                                      />
                                      {groupedPackages[1].count > 1 && (
                                        <div className="absolute -top-2 -right-2 bg-black/70 backdrop-blur-sm border border-orange-400/50 text-orange-300 text-xs font-bold rounded-full w-7 h-7 flex items-center justify-center shadow-lg">
                                          {groupedPackages[1].count}
                                        </div>
                                      )}
                                    </div>
                                    <div className="flex items-center gap-1">
                                      <Image
                                        src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/rp.svg"
                                        alt="RP"
                                        width={16}
                                        height={16}
                                      />
                                      <span className="text-orange-400 font-medium text-sm">
                                        {groupedPackages[1].rp.toLocaleString()}
                                      </span>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            )}
                          </div>

                          {/* Summary - compact */}
                          <div className="text-center border-t border-orange-700/30 pt-2">
                            <div className="flex items-center justify-center gap-2 text-orange-400 font-bold text-sm">
                              <div className="flex items-center gap-1">
                                <Image
                                  src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/rp.svg"
                                  alt="RP"
                                  width={16}
                                  height={16}
                                />
                                <span>{totalRp.toLocaleString()}</span>
                              </div>
                              {excessRp > 0 && (
                                <span className="text-green-400 text-sm">
                                  (+{excessRp.toLocaleString()})
                                </span>
                              )}
                            </div>
                            <div className="text-white mt-1 text-sm">
                              {currentRealCurrency.symbol}{totalCost.toFixed(2)}
                            </div>
                          </div>
                        </div>
                      )
                    })()
                  ) : (
                    <div className="text-center text-gray-400 text-xs flex flex-col justify-center h-full">
                      <div className="text-3xl mb-3">📦</div>
                      <div className="text-xs leading-relaxed">
                        {conversionMode !== 'ingame-to-real' ? (
                          'Available in Real Currency tab only'
                        ) : selectedInGameCurrency !== 'rp' ? (
                          'Select RP as input currency'
                        ) : (
                          'Enter RP amount to see recommendations'
                        )}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
              </div>
            )}

            </div>
          </div>

          {/* How Does This Work Section */}
          <div className="max-w-4xl mx-auto mt-12">
            <Card className="bg-gray-900/60 border-blue-700/30 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-3">
                  <Info className="h-6 w-6 text-blue-400" />
                  How Does This Work?
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 text-gray-300">
                <div className="space-y-3">
                  <p className="text-lg font-medium text-white">
                    Currency Conversion Rates
                  </p>
                  <p>
                    Our conversion tool uses <strong className="text-blue-400">official server pricings</strong> as the foundation for all calculations. All in-game currencies are first converted to RP, then to the selected currency using official pricing tiers.
                  </p>
                </div>

                <div className="space-y-3">
                  <p className="text-lg font-medium text-white">
                    Regional Support
                  </p>
                  <p>
                    Currently featuring <strong className="text-orange-400">EU, UK, NA, ME, OCE, BR & TR server rates</strong> with support for all in-game currencies to EUR, GBP, USD, AUD, BRL & TRY conversion. Additional currencies are supported via live exchange rates. <strong className="text-green-400">Multi-currency support available!</strong>
                  </p>
                  {/* Debug info - remove in production */}
                  <div className="text-xs text-gray-500">
                    Exchange rates: {liveExchangeRates ? `${Object.keys(liveExchangeRates).length} currencies loaded` : exchangeRatesLoading ? 'Loading...' : 'Not loaded'}
                  </div>
                </div>

                <div className="space-y-3">
                  <p className="text-lg font-medium text-white">
                    In-Game Currency Estimates
                  </p>
                  <ul className="space-y-2 ml-4">
                    <li className="flex items-center gap-2">
                      <span className="w-2 h-2 bg-purple-400 rounded-full"></span>
                      <strong>Ancient Sparks:</strong> Premium currency for Exalted skins (1 AS = 400 RP)
                    </li>
                    <li className="flex items-center gap-2">
                      <span className="w-2 h-2 bg-purple-400 rounded-full"></span>
                      <strong>Mythic Essence:</strong> Used for prestige and mythic content (~69.5 RP = 1 ME)
                    </li>
                    <li className="flex items-center gap-2">
                      <span className="w-2 h-2 bg-blue-400 rounded-full"></span>
                      <strong>Blue Essence:</strong> Free currency for champions (~14.5 BE = 1 RP)
                    </li>
                    <li className="flex items-center gap-2">
                      <span className="w-2 h-2 bg-orange-400 rounded-full"></span>
                      <strong>Orange Essence:</strong> Currency for skin upgrades (~0.67 OE = 1 RP)
                    </li>
                  </ul>

                  <div className="bg-amber-900/20 border border-amber-700/30 rounded-lg p-3 mt-4">
                    <p className="text-amber-300 text-sm">
                      <strong>⚠️ Important:</strong> You cannot directly exchange between currencies or purchase Mythic Essence, Orange Essence, or Blue Essence with RP. These are <strong>estimates based on in-game shop bundle values</strong> to help you understand relative worth.
                    </p>
                  </div>
                </div>

                <div className="bg-orange-900/20 border border-orange-700/30 rounded-lg p-4 mt-6">
                  <p className="text-orange-300 font-medium flex items-center gap-2">
                    <span className="text-orange-400">🚀</span>
                    Coming Soon: USD, GBP, CAD, AUD, and 15+ more currencies!
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

        </div>
      </SharedLayout>
    </ConversionBackground>
  )
}
